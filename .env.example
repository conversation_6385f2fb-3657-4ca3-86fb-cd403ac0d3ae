# Environment Configuration Example
# Copy this file to .env and fill in your actual values

# Project Configuration
PROJECT_NAME=airline-passengers-forecasting
PROJECT_VERSION=1.0.0

# Data Configuration
DATA_PATH=data/
MODEL_OUTPUT_PATH=models/
RESULTS_PATH=results/

# Model Parameters
TRAIN_TEST_SPLIT_RATIO=0.8
RANDOM_STATE=42

# ARIMA Configuration
ARIMA_ORDER_P=2
ARIMA_ORDER_D=1
ARIMA_ORDER_Q=2

# Exponential Smoothing Configuration
EXP_SMOOTH_TREND=add
EXP_SMOOTH_SEASONAL=add
EXP_SMOOTH_SEASONAL_PERIODS=12

# Forecasting Configuration
FORECAST_PERIODS=12
CONFIDENCE_LEVEL=0.95

# Visualization Configuration
FIGURE_SIZE_WIDTH=15
FIGURE_SIZE_HEIGHT=10
DPI=300

# Jupyter Configuration
JUPYTER_PORT=8888
JUPYTER_HOST=localhost

# Optional: API Keys (if using external data sources)
# WEATHER_API_KEY=your_weather_api_key_here
# ECONOMIC_DATA_API_KEY=your_economic_api_key_here

# Optional: Database Configuration (if storing results)
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=forecasting_db
# DB_USER=your_username
# DB_PASSWORD=your_password
