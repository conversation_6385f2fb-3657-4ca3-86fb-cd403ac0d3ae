# Data manipulation and analysis
import pandas as pd
import numpy as np

# Visualization
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Time series analysis
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf

# Machine learning
from sklearn.metrics import mean_squared_error, mean_absolute_error
from sklearn.preprocessing import MinMaxScaler

# Warnings
import warnings
warnings.filterwarnings('ignore')

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

print("Libraries imported successfully!")

# Load the dataset
df = pd.read_csv('data/AirPassengers.csv')

# Display basic information
print("Dataset Shape:", df.shape)
print("\nFirst 5 rows:")
print(df.head())
print("\nLast 5 rows:")
print(df.tail())
print("\nDataset Info:")
print(df.info())
print("\nBasic Statistics:")
print(df.describe())

# Data preprocessing
# Convert Month to datetime
df['Month'] = pd.to_datetime(df['Month'])
df.set_index('Month', inplace=True)

# Rename column for easier access
df.rename(columns={'#Passengers': 'Passengers'}, inplace=True)

print("Data preprocessing completed!")
print("\nProcessed dataset:")
print(df.head())
print(f"\nDate range: {df.index.min()} to {df.index.max()}")
print(f"Total months: {len(df)}")

# Basic time series plot
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Original time series
axes[0, 0].plot(df.index, df['Passengers'], linewidth=2)
axes[0, 0].set_title('Airline Passengers Over Time')
axes[0, 0].set_xlabel('Year')
axes[0, 0].set_ylabel('Number of Passengers')
axes[0, 0].grid(True, alpha=0.3)

# Distribution of passengers
axes[0, 1].hist(df['Passengers'], bins=20, alpha=0.7, edgecolor='black')
axes[0, 1].set_title('Distribution of Passenger Numbers')
axes[0, 1].set_xlabel('Number of Passengers')
axes[0, 1].set_ylabel('Frequency')
axes[0, 1].grid(True, alpha=0.3)

# Box plot by year
df_year = df.copy()
df_year['Year'] = df_year.index.year
df_year.boxplot(column='Passengers', by='Year', ax=axes[1, 0])
axes[1, 0].set_title('Passenger Numbers by Year')
axes[1, 0].set_xlabel('Year')
axes[1, 0].set_ylabel('Number of Passengers')

# Monthly seasonality
df_month = df.copy()
df_month['Month'] = df_month.index.month
monthly_avg = df_month.groupby('Month')['Passengers'].mean()
axes[1, 1].plot(monthly_avg.index, monthly_avg.values, marker='o', linewidth=2)
axes[1, 1].set_title('Average Passengers by Month')
axes[1, 1].set_xlabel('Month')
axes[1, 1].set_ylabel('Average Passengers')
axes[1, 1].set_xticks(range(1, 13))
axes[1, 1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Perform seasonal decomposition
decomposition = seasonal_decompose(df['Passengers'], model='multiplicative', period=12)

# Plot decomposition
fig, axes = plt.subplots(4, 1, figsize=(15, 12))

decomposition.observed.plot(ax=axes[0], title='Original Time Series')
decomposition.trend.plot(ax=axes[1], title='Trend Component')
decomposition.seasonal.plot(ax=axes[2], title='Seasonal Component')
decomposition.resid.plot(ax=axes[3], title='Residual Component')

for ax in axes:
    ax.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print("Time series shows:")
print("1. Clear upward trend")
print("2. Strong seasonal pattern (yearly cycle)")
print("3. Increasing variance over time")

# Augmented Dickey-Fuller test for stationarity
def check_stationarity(timeseries, title):
    print(f'Results of Augmented Dickey-Fuller Test for {title}:')
    dftest = adfuller(timeseries, autolag='AIC')
    dfoutput = pd.Series(dftest[0:4], index=['Test Statistic','p-value','#Lags Used','Number of Observations Used'])
    for key,value in dftest[4].items():
        dfoutput['Critical Value (%s)'%key] = value
    print(dfoutput)
    
    if dftest[1] <= 0.05:
        print("Series is stationary")
    else:
        print("Series is non-stationary")
    print("-"*50)

# Test original series
check_stationarity(df['Passengers'], 'Original Series')

# Apply log transformation
df['Passengers_log'] = np.log(df['Passengers'])
check_stationarity(df['Passengers_log'], 'Log Transformed Series')

# Apply first differencing to log series
df['Passengers_log_diff'] = df['Passengers_log'].diff().dropna()
check_stationarity(df['Passengers_log_diff'].dropna(), 'Log Differenced Series')

# Plot ACF and PACF for the stationary series
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Original series
plot_acf(df['Passengers'].dropna(), ax=axes[0, 0], lags=40, title='ACF - Original Series')
plot_pacf(df['Passengers'].dropna(), ax=axes[0, 1], lags=40, title='PACF - Original Series')

# Differenced series
plot_acf(df['Passengers_log_diff'].dropna(), ax=axes[1, 0], lags=40, title='ACF - Log Differenced Series')
plot_pacf(df['Passengers_log_diff'].dropna(), ax=axes[1, 1], lags=40, title='PACF - Log Differenced Series')

plt.tight_layout()
plt.show()

# Split data into train and test sets
train_size = int(len(df) * 0.8)
train_data = df[:train_size]
test_data = df[train_size:]

print(f"Training data: {len(train_data)} months ({train_data.index[0]} to {train_data.index[-1]})")
print(f"Testing data: {len(test_data)} months ({test_data.index[0]} to {test_data.index[-1]})")

# Visualize the split
plt.figure(figsize=(12, 6))
plt.plot(train_data.index, train_data['Passengers'], label='Training Data', color='blue')
plt.plot(test_data.index, test_data['Passengers'], label='Testing Data', color='red')
plt.title('Train-Test Split')
plt.xlabel('Year')
plt.ylabel('Number of Passengers')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

# Fit ARIMA model
# Using ARIMA(2,1,2) based on ACF/PACF analysis
arima_model = ARIMA(train_data['Passengers'], order=(2,1,2))
arima_fitted = arima_model.fit()

print("ARIMA Model Summary:")
print(arima_fitted.summary())

# Make predictions
arima_forecast = arima_fitted.forecast(steps=len(test_data))
arima_forecast_index = test_data.index

# Calculate metrics
arima_mse = mean_squared_error(test_data['Passengers'], arima_forecast)
arima_mae = mean_absolute_error(test_data['Passengers'], arima_forecast)
arima_rmse = np.sqrt(arima_mse)

print(f"\nARIMA Model Performance:")
print(f"MSE: {arima_mse:.2f}")
print(f"MAE: {arima_mae:.2f}")
print(f"RMSE: {arima_rmse:.2f}")

# Fit Exponential Smoothing model
exp_smooth_model = ExponentialSmoothing(
    train_data['Passengers'], 
    trend='add', 
    seasonal='add', 
    seasonal_periods=12
)
exp_smooth_fitted = exp_smooth_model.fit()

# Make predictions
exp_smooth_forecast = exp_smooth_fitted.forecast(steps=len(test_data))

# Calculate metrics
exp_smooth_mse = mean_squared_error(test_data['Passengers'], exp_smooth_forecast)
exp_smooth_mae = mean_absolute_error(test_data['Passengers'], exp_smooth_forecast)
exp_smooth_rmse = np.sqrt(exp_smooth_mse)

print(f"Exponential Smoothing Model Performance:")
print(f"MSE: {exp_smooth_mse:.2f}")
print(f"MAE: {exp_smooth_mae:.2f}")
print(f"RMSE: {exp_smooth_rmse:.2f}")

# Create comparison plot
plt.figure(figsize=(15, 8))

# Plot actual data
plt.plot(df.index, df['Passengers'], label='Actual', color='black', linewidth=2)

# Plot training data
plt.plot(train_data.index, train_data['Passengers'], label='Training Data', color='blue', alpha=0.7)

# Plot forecasts
plt.plot(test_data.index, arima_forecast, label='ARIMA Forecast', color='red', linestyle='--', linewidth=2)
plt.plot(test_data.index, exp_smooth_forecast, label='Exp. Smoothing Forecast', color='green', linestyle='--', linewidth=2)

# Add vertical line to separate train/test
plt.axvline(x=train_data.index[-1], color='gray', linestyle=':', alpha=0.7, label='Train/Test Split')

plt.title('Airline Passengers Forecasting - Model Comparison')
plt.xlabel('Year')
plt.ylabel('Number of Passengers')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

# Model performance comparison
models_performance = pd.DataFrame({
    'Model': ['ARIMA', 'Exponential Smoothing'],
    'MSE': [arima_mse, exp_smooth_mse],
    'MAE': [arima_mae, exp_smooth_mae],
    'RMSE': [arima_rmse, exp_smooth_rmse]
})

print("\nModel Performance Comparison:")
print(models_performance)

# Make future predictions (next 12 months)
future_periods = 12

# Refit models on full dataset for future predictions
arima_full = ARIMA(df['Passengers'], order=(2,1,2)).fit()
exp_smooth_full = ExponentialSmoothing(
    df['Passengers'], 
    trend='add', 
    seasonal='add', 
    seasonal_periods=12
).fit()

# Generate future dates
last_date = df.index[-1]
future_dates = pd.date_range(start=last_date + pd.DateOffset(months=1), periods=future_periods, freq='MS')

# Make predictions
arima_future = arima_full.forecast(steps=future_periods)
exp_smooth_future = exp_smooth_full.forecast(steps=future_periods)

# Create future predictions dataframe
future_predictions = pd.DataFrame({
    'Date': future_dates,
    'ARIMA_Forecast': arima_future,
    'ExpSmooth_Forecast': exp_smooth_future
})
future_predictions.set_index('Date', inplace=True)

print("Future Predictions (Next 12 months):")
print(future_predictions)

# Plot future predictions
plt.figure(figsize=(15, 8))

# Plot historical data
plt.plot(df.index, df['Passengers'], label='Historical Data', color='black', linewidth=2)

# Plot future predictions
plt.plot(future_predictions.index, future_predictions['ARIMA_Forecast'], 
         label='ARIMA Future Forecast', color='red', linestyle='--', linewidth=2, marker='o')
plt.plot(future_predictions.index, future_predictions['ExpSmooth_Forecast'], 
         label='Exp. Smoothing Future Forecast', color='green', linestyle='--', linewidth=2, marker='s')

# Add vertical line to separate historical/future
plt.axvline(x=df.index[-1], color='gray', linestyle=':', alpha=0.7, label='Historical/Future Split')

plt.title('Airline Passengers - Future Predictions')
plt.xlabel('Year')
plt.ylabel('Number of Passengers')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()